
import pandas as pd
import os
import re

def normalize_carrier_name(name):
    """Normalize carrier name for matching"""
    if not isinstance(name, str):
        return ''
    name = name.lower()
    name = re.sub(r'[^\w\s]', '', name)
    name = re.sub(r'\s+(inc|llc|corp|ltd|incorporated|company|services|logistics|transport|trucking|transportation|express|freight|systems|solutions|group|carriers|lines|delivery|service|international|national|system|carrier|line)$', '', name)
    name = name.strip()
    return name

# Define file paths
attachment_csv_path = '/home/<USER>/Documents/repositories/logistically/notebooks/attachment_type_by_carrier_name__.csv'
file_list_csv_path = '/home/<USER>/Documents/repositories/logistically/notebooks/file_list.csv'
output_csv_path = '/home/<USER>/Documents/repositories/logistically/notebooks/attachment_type_by_carrier_name__v2.csv'

try:
    # Read the original attachment CSV to get all attachment types and carrier combinations
    attachment_df = pd.read_csv(attachment_csv_path)

    # Read the file list CSV to get actual paths for invoices
    file_list_df = pd.read_csv(file_list_csv_path)

    # Extract carrier name from directory path in file_list
    file_list_df['carrier_name'] = file_list_df['Path'].apply(
        lambda x: os.path.basename(os.path.dirname(x))
    )

    # Create normalized carrier names for matching
    attachment_df['normalized_carrier_name'] = attachment_df['carrier_name'].apply(normalize_carrier_name)
    file_list_df['normalized_carrier_name'] = file_list_df['carrier_name'].apply(normalize_carrier_name)

    # Group file_list by carrier_name to get one path per carrier
    file_paths_by_carrier = file_list_df.groupby('normalized_carrier_name').first().reset_index()

    # Create a mapping from normalized carrier name to actual path
    carrier_to_path = dict(zip(file_paths_by_carrier['normalized_carrier_name'], file_paths_by_carrier['Path']))
    carrier_to_filename = dict(zip(file_paths_by_carrier['normalized_carrier_name'], file_paths_by_carrier['File Name']))

    # For each row in attachment_df, get one representative entry per attachment_type + carrier_name combination
    grouped_attachment = attachment_df.groupby(['attachment_type', 'carrier_name']).first().reset_index()

    # Create the final output
    final_rows = []

    for _, row in grouped_attachment.iterrows():
        attachment_type = row['attachment_type']
        carrier_name = row['carrier_name']
        normalized_name = row['normalized_carrier_name']

        # Try to get actual path for this carrier
        if normalized_name in carrier_to_path:
            path_to_file = carrier_to_path[normalized_name]
            original_file_name = carrier_to_filename[normalized_name]
        else:
            # If no actual path available, create a placeholder
            path_to_file = f"/path/to/{attachment_type}/{carrier_name}/sample_file.pdf"
            original_file_name = f"sample_{attachment_type}_file.pdf"

        final_rows.append({
            'attachment_type': attachment_type,
            'carrier_name': carrier_name,
            'path_to_file': path_to_file,
            'original_file_name': original_file_name,
            'carrier_id': None,
            'order_id': None,
            'order_number': None,
            'invoice_number': None,
            'invoice_date': None,
            'invoice_amount': None,
            'vendor_name': None,
            'remit_to_name': None,
            'remit_to_address1': None,
            'remit_to_address_2': None,
            'remit_to_city': None,
            'remit_to_state_province': None,
            'remit_to_postal_code': None,
            'remit_to_country': None
        })

    # Create final DataFrame
    final_df = pd.DataFrame(final_rows)

    # Save the final DataFrame to a new CSV file
    final_df.to_csv(output_csv_path, index=False)

    print(f"Successfully created '{output_csv_path}' with {len(final_df)} rows.")
    print(f"Found {final_df['attachment_type'].nunique()} unique attachment types:")
    print(final_df['attachment_type'].value_counts().to_string())
    print(f"\nFound {final_df['carrier_name'].nunique()} unique carrier names.")

    # Show some statistics about actual vs placeholder paths
    actual_paths = final_df['path_to_file'].str.contains('/home/<USER>/').sum()
    placeholder_paths = len(final_df) - actual_paths
    print(f"\nPaths with actual file locations: {actual_paths}")
    print(f"Paths with placeholder locations: {placeholder_paths}")

except FileNotFoundError as e:
    print(f"Error: {e}")
except Exception as e:
    print(f"An error occurred: {e}")
